# Store Analysis 性能优化指南

## 主要优化内容

### 1. 向量化操作替代循环
**原问题**: 使用 `iterrows()` 逐行处理数据，效率极低
**优化方案**: 使用pandas向量化操作和布尔索引

```python
# 原始版本 - 慢
for _, row in chunk_data.iterrows():
    if row[cat] > 0:
        # 处理逻辑

# 优化版本 - 快
mask = df[cat] > 0
if mask.any():
    temp_df = df[mask][base_columns].copy()
```

### 2. 减少数据扩展次数
**原问题**: 在多重循环中重复调用数据扩展函数
**优化方案**: 预先扩展所有需要的数据，避免重复计算

```python
# 预先扩展数据（只扩展一次）
df_category = flatten_category_data_vectorized(df)
df_poi = flatten_poi_data_vectorized(df)
df_category_poi = flatten_category_and_poi_data_vectorized(df)
```

### 3. 优化筛选操作
**原问题**: 多次复制DataFrame进行筛选
**优化方案**: 使用向量化筛选，减少内存复制

```python
def apply_filters_vectorized(df, **filters):
    mask = pd.Series(True, index=df.index)
    for key, value in filters.items():
        if value is not None and key in df.columns:
            mask &= (df[key] == value)
    return df[mask]
```

### 4. 减少循环嵌套层数
**原问题**: 7层嵌套循环，时间复杂度过高
**优化方案**: 重构循环结构，提前过滤空数据

## 性能提升预期

对于600万行数据：
- **原始版本**: 可能需要数小时
- **优化版本**: 预计减少到几十分钟

主要提升来源：
1. 向量化操作: 10-100倍性能提升
2. 减少数据扩展: 避免重复计算
3. 内存优化: 减少DataFrame复制
4. 早期过滤: 减少无效计算

## 使用方法

```python
# 使用优化版本（推荐）
result = main(df, df2, current_month="2025_05", use_optimized=True)

# 性能测试
result, exec_time = benchmark_performance(df, df2, current_month="2025_05")
print(f"执行时间: {exec_time:.2f}秒")
```

## 注意事项

1. 确保数据列名正确
2. 内存使用会增加（因为预先扩展数据）
3. 如果遇到问题，可以设置 `use_optimized=False` 使用原始版本
4. 建议先用小数据集测试

## 进一步优化建议

1. **使用Dask**: 对于超大数据集，可以考虑使用Dask进行分布式计算
2. **数据库优化**: 将部分计算推到数据库层面
3. **缓存机制**: 对重复查询结果进行缓存
4. **并行处理**: 对独立的省份或城市进行并行处理
