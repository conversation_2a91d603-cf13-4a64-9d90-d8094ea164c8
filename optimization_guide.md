# Store Analysis 多进程性能优化指南

## 🚀 最新优化内容

### 1. 多进程并行处理
**核心改进**: 按省份分割数据，使用多进程并行处理
**性能提升**: 在多核CPU上可获得接近线性的性能提升

```python
# 多进程处理 - 推荐
result = main(df, df2, method='multiprocess', num_cores=16)

# 自动选择最佳方法
result = main(df, df2, method='auto')  # 根据数据量自动选择
```

### 2. 分块处理策略
**适用场景**: 超大数据集（>100万行）
**优势**: 避免内存溢出，支持更大数据集

```python
# 分块多进程处理
result = main(df, df2, method='chunked', chunk_size=100000)
```

### 3. 向量化操作替代循环
**原问题**: 使用 `iterrows()` 逐行处理数据，效率极低
**优化方案**: 使用pandas向量化操作和布尔索引

```python
# 原始版本 - 慢
for _, row in chunk_data.iterrows():
    if row[cat] > 0:
        # 处理逻辑

# 优化版本 - 快
mask = df[cat] > 0
if mask.any():
    temp_df = df[mask][base_columns].copy()
```

### 4. 智能数据预处理
**策略**: 按省份预先扩展数据，减少重复计算
**效果**: 避免在循环中重复进行数据扩展操作

## 📊 性能对比

### 处理方法选择
| 数据量 | 推荐方法 | 预期性能 |
|--------|----------|----------|
| < 10万行 | single | 几分钟 |
| 10万-100万行 | multiprocess | 几十分钟 |
| > 100万行 | chunked | 1-2小时 |

### 多核性能提升
对于600万行数据的预期性能：
- **单进程**: 8-12小时
- **4核多进程**: 2-3小时 (3-4x提升)
- **8核多进程**: 1-1.5小时 (6-8x提升)
- **16核多进程**: 30-45分钟 (10-15x提升)

## 🛠️ 使用方法

### 基本使用
```python
# 自动选择最佳处理方法（推荐）
result = main(df, df2, current_month="2025_05", method='auto')

# 手动指定多进程处理
result = main(df, df2, method='multiprocess', num_cores=16)

# 超大数据集使用分块处理
result = main(df, df2, method='chunked', chunk_size=50000, num_cores=8)
```

### 性能基准测试
```python
# 测试不同核心数的性能
results = benchmark_performance(df, df2, test_cores=[1, 4, 8, 16])

# 输出性能对比
for method, stats in results.items():
    print(f"{method}: {stats['time']:.2f}秒, {stats['speed']:.0f}行/秒")
```

## ⚡ 核心优化技术

### 1. 省份级并行化
- 将数据按省份分割
- 每个进程处理一个或多个省份
- 最后合并结果

### 2. 内存优化
- 使用字典传递数据到子进程
- 避免大对象的pickle序列化开销
- 及时释放不需要的中间结果

### 3. 负载均衡
- 根据省份数据量动态分配任务
- 避免某些进程空闲而其他进程过载

## 📋 注意事项

### 系统要求
1. **CPU**: 多核CPU效果更佳（推荐8核以上）
2. **内存**: 建议32GB以上（600万行数据）
3. **存储**: SSD硬盘可提升I/O性能

### 使用建议
1. **先测试小数据集**: 确保逻辑正确性
2. **监控系统资源**: 避免内存不足或CPU过载
3. **调整参数**: 根据实际硬件调整核心数和块大小
4. **备用方案**: 如有问题可使用 `method='single'`

### 常见问题
- **内存不足**: 减少 `chunk_size` 或 `num_cores`
- **进程卡死**: 检查数据是否有异常值
- **结果不一致**: 确保所有进程使用相同的参数

## 🔧 进一步优化建议

1. **GPU加速**: 使用cuDF进行GPU并行计算
2. **分布式计算**: 使用Dask或Ray进行集群计算
3. **数据库优化**: 将计算推到数据库层面
4. **缓存策略**: 对重复查询结果进行缓存
5. **增量计算**: 只处理变化的数据部分
