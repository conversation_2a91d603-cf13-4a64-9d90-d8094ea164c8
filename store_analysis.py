import pandas as pd
import numpy as np
from itertools import combinations, product
from multiprocessing import Pool
import logging

logger = logging.getLogger(__name__)

def flatten_category_data(chunk_data):
    """只扩展品类数据"""
    chunk_expanded = []
    category_columns = ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                        '个人护理', '家庭护理', '粮油速食', '孕婴用品',
                        '宠物用品', '水饮冲调', '土特产']
    
    for _, row in chunk_data.iterrows():
        base_data = {
            'province': row['province'],
            'city': row['city'],
            '面积': row['面积'],
            'FMCG_Type': row['FMCG_Type'],
            'FMCG_Chaintype': row['FMCG_Chaintype'],
            'is_chainstore': row['is_chainstore'],
            'FMCG_Channel': row['FMCG_Channel'],
            'FMCG_Sub_Channel': row['FMCG_Sub_Channel'],
            'create_time': row['create_time'],
            'close_time': row['close_time']
        }
        
        for cat in category_columns:
            if cat == "综合":
                if row["综合"] > 0:
                    record = base_data.copy()
                    record['品类'] = cat
                    chunk_expanded.append(record)
            elif cat == "土特产":
                if row["土特产"] > 0:
                    record = base_data.copy()
                    record['品类'] = cat
                    chunk_expanded.append(record)
            else:
                if row[cat] > 0 or row["综合"] > 0:
                    record = base_data.copy()
                    record['品类'] = cat
                    chunk_expanded.append(record)
    
    return chunk_expanded

def flatten_poi_data(chunk_data):
    """只扩展POI数据"""
    chunk_expanded = []
    poi_columns = ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店', 
                   '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']
    
    for _, row in chunk_data.iterrows():
        base_data = {
            'province': row['province'],
            'city': row['city'],
            '面积': row['面积'],
            'FMCG_Type': row['FMCG_Type'],
            'FMCG_Chaintype': row['FMCG_Chaintype'],
            'is_chainstore': row['is_chainstore'],
            'FMCG_Channel': row['FMCG_Channel'],
            'FMCG_Sub_Channel': row['FMCG_Sub_Channel'],
            'create_time': row['create_time'],
            'close_time': row['close_time']
        }
        
        for poi in poi_columns:
            if row[poi] > 0:
                record = base_data.copy()
                record['poi'] = poi
                chunk_expanded.append(record)
    
    return chunk_expanded

def flatten_category_and_poi_data(chunk_data):
    """品类和POI数据完整扩展"""
    chunk_expanded = []
    category_columns = ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                        '个人护理', '家庭护理', '粮油速食', '孕婴用品',
                        '宠物用品', '水饮冲调', '土特产']
    poi_columns = ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店', 
                   '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']
    
    for _, row in chunk_data.iterrows():
        base_data = {
            'province': row['province'],
            'city': row['city'],
            '面积': row['面积'],
            'FMCG_Type': row['FMCG_Type'],
            'FMCG_Chaintype': row['FMCG_Chaintype'],
            'is_chainstore': row['is_chainstore'],
            'FMCG_Channel': row['FMCG_Channel'],
            'FMCG_Sub_Channel': row['FMCG_Sub_Channel'],
            'create_time': row['create_time'],
            'close_time': row['close_time']
        }
        
        # 处理品类和POI组合
        for cat in category_columns:
            for poi in poi_columns:
                if cat == "综合":
                    if row["综合"] > 0 and row[poi] > 0:
                        record = base_data.copy()
                        record['品类'] = cat
                        record['poi'] = poi
                        chunk_expanded.append(record)
                elif cat == "土特产":
                    if row["土特产"] > 0 and row[poi] > 0:
                        record = base_data.copy()
                        record['品类'] = cat
                        record['poi'] = poi
                        chunk_expanded.append(record)
                else:
                    if (row[cat] > 0 or row["综合"] > 0) and row[poi] > 0:
                        record = base_data.copy()
                        record['品类'] = cat
                        record['poi'] = poi
                        chunk_expanded.append(record)
    
    return chunk_expanded

def parallel_process_category_df(df, num_cores=22):
    """并行处理品类扩展"""
    if len(df) == 0:
        return pd.DataFrame()
    
    chunk_size = max(1, len(df) // num_cores)
    chunks = [df[i:i + chunk_size] for i in range(0, len(df), chunk_size)]
    
    with Pool(num_cores) as pool:
        results = pool.map(flatten_category_data, chunks)
    
    all_expanded = []
    for result in results:
        all_expanded.extend(result)
    
    return pd.DataFrame(all_expanded)

def parallel_process_poi_df(df, num_cores=22):
    """并行处理POI扩展"""
    if len(df) == 0:
        return pd.DataFrame()
    
    chunk_size = max(1, len(df) // num_cores)
    chunks = [df[i:i + chunk_size] for i in range(0, len(df), chunk_size)]
    
    with Pool(num_cores) as pool:
        results = pool.map(flatten_poi_data, chunks)
    
    all_expanded = []
    for result in results:
        all_expanded.extend(result)
    
    return pd.DataFrame(all_expanded)

def parallel_process_slice_df(df, num_cores=22):
    """并行处理品类和POI完整扩展"""
    if len(df) == 0:
        return pd.DataFrame()
    
    chunk_size = max(1, len(df) // num_cores)
    chunks = [df[i:i + chunk_size] for i in range(0, len(df), chunk_size)]
    
    with Pool(num_cores) as pool:
        results = pool.map(flatten_category_and_poi_data, chunks)
    
    all_expanded = []
    for result in results:
        all_expanded.extend(result)
    
    return pd.DataFrame(all_expanded)

def get_appropriate_dataframe(df, category, poi):
    """根据需求获取合适的数据框"""
    if category is not None and poi is not None:
        # 需要品类和POI：使用完整扩展
        return parallel_process_slice_df(df)
    elif category is not None and poi is None:
        # 只需要品类：只扩展品类
        return parallel_process_category_df(df)
    elif category is None and poi is not None:
        # 只需要POI：只扩展POI
        return parallel_process_poi_df(df)
    else:
        # 都不需要：使用原始数据
        return df

def apply_filters(df, province=None, city=None, area=None, is_chainstore=None, 
                 fmcg_type=None, fmcg_chaintype=None, fmcg_channel=None, fmcg_sub_channel=None):
    """应用筛选条件"""
    filtered_df = df.copy()
    
    if province is not None:
        filtered_df = filtered_df[filtered_df['province'] == province]
    if city is not None:
        filtered_df = filtered_df[filtered_df['city'] == city]
    if area is not None:
        filtered_df = filtered_df[filtered_df['面积'] == area]
    if is_chainstore is not None:
        filtered_df = filtered_df[filtered_df['is_chainstore'] == is_chainstore]
    if fmcg_type is not None:
        filtered_df = filtered_df[filtered_df['FMCG_Type'] == fmcg_type]
    if fmcg_chaintype is not None:
        filtered_df = filtered_df[filtered_df['FMCG_Chaintype'] == fmcg_chaintype]
    if fmcg_channel is not None:
        filtered_df = filtered_df[filtered_df['FMCG_Channel'] == fmcg_channel]
    if fmcg_sub_channel is not None:
        filtered_df = filtered_df[filtered_df['FMCG_Sub_Channel'] == fmcg_sub_channel]
    
    return filtered_df

def generate_comprehensive_statistics(df, df2, current_month):
    """生成全面的统计数据"""
    all_results = []
    
    # 获取所有可能的值
    provinces = [None] + list(df['province'].dropna().unique())
    areas = [None, '小90', '90-300', '300-500', '500-1000', '1000-2000', '>2000']
    categories = [None] + ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                          '个人护理', '家庭护理', '粮油速食', '孕婴用品', '宠物用品', '水饮冲调', '土特产']
    pois = [None] + ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店', 
                    '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']
    chainstores = [None, 0, 1]
    
    for province in provinces:
        # 根据省份筛选城市
        if province is None:
            cities = [None]
        else:
            cities = [None] + list(df[df['province'] == province]['city'].dropna().unique())
        
        for city in cities:
            for area in areas:
                for category in categories:
                    for poi in pois:
                        for is_chainstore in chainstores:
                            # 基础筛选
                            base_df = apply_filters(df, province, city, area, is_chainstore)
                            base_df2 = apply_filters(df2, province, city, area, is_chainstore)
                            
                            # 根据需求获取合适的数据框
                            current_df = get_appropriate_dataframe(base_df, category, poi)
                            current_df2 = get_appropriate_dataframe(base_df2, category, poi)
                            
                            # 应用品类和POI筛选
                            if category is not None:
                                current_df = current_df[current_df['品类'] == category]
                                current_df2 = current_df2[current_df2['品类'] == category]
                            if poi is not None:
                                current_df = current_df[current_df['poi'] == poi]
                                current_df2 = current_df2[current_df2['poi'] == poi]
                            
                            # FMCG_Type 和 FMCG_Chaintype 组合
                            fmcg_types = [None] + list(current_df['FMCG_Type'].dropna().unique()) if len(current_df) > 0 else [None]
                            for fmcg_type in fmcg_types:
                                if fmcg_type is None:
                                    fmcg_chaintypes = [None]
                                else:
                                    fmcg_chaintypes = [None] + list(current_df[current_df['FMCG_Type'] == fmcg_type]['FMCG_Chaintype'].dropna().unique())
                                
                                for fmcg_chaintype in fmcg_chaintypes:
                                    # 应用FMCG筛选
                                    filter_df = apply_filters(current_df, fmcg_type=fmcg_type, fmcg_chaintype=fmcg_chaintype)
                                    filter_df2 = apply_filters(current_df2, fmcg_type=fmcg_type, fmcg_chaintype=fmcg_chaintype)
                                    
                                    result_row = {
                                        'province': province,
                                        'city': city,
                                        'FMCG_Type': fmcg_type,
                                        'FMCG_Chaintype': fmcg_chaintype,
                                        'FMCG_Channel': None,
                                        'FMCG_Sub_Channel': None,
                                        'is_chainstore': is_chainstore,
                                        '面积': area,
                                        '品类': category,
                                        'poi': poi,
                                        '门店数量': len(filter_df),
                                        '开店数': len(filter_df2[filter_df2['create_time'] == current_month]),
                                        '闭店数': len(filter_df[filter_df['close_time'] == current_month])
                                    }
                                    
                                    if result_row['门店数量'] > 0 or result_row['开店数'] > 0 or result_row['闭店数'] > 0:
                                        all_results.append(result_row)
                            
                            # FMCG_Channel 和 FMCG_Sub_Channel 组合
                            fmcg_channels = [None] + list(current_df['FMCG_Channel'].dropna().unique()) if len(current_df) > 0 else [None]
                            for fmcg_channel in fmcg_channels:
                                if fmcg_channel is None:
                                    fmcg_sub_channels = [None]
                                else:
                                    fmcg_sub_channels = [None] + list(current_df[current_df['FMCG_Channel'] == fmcg_channel]['FMCG_Sub_Channel'].dropna().unique())
                                
                                for fmcg_sub_channel in fmcg_sub_channels:
                                    # 应用Channel筛选
                                    filter_df = apply_filters(current_df, fmcg_channel=fmcg_channel, fmcg_sub_channel=fmcg_sub_channel)
                                    filter_df2 = apply_filters(current_df2, fmcg_channel=fmcg_channel, fmcg_sub_channel=fmcg_sub_channel)
                                    
                                    result_row = {
                                        'province': province,
                                        'city': city,
                                        'FMCG_Type': None,
                                        'FMCG_Chaintype': None,
                                        'FMCG_Channel': fmcg_channel,
                                        'FMCG_Sub_Channel': fmcg_sub_channel,
                                        'is_chainstore': is_chainstore,
                                        '面积': area,
                                        '品类': category,
                                        'poi': poi,
                                        '门店数量': len(filter_df),
                                        '开店数': len(filter_df2[filter_df2['create_time'] == current_month]),
                                        '闭店数': len(filter_df[filter_df['close_time'] == current_month])
                                    }
                                    
                                    if result_row['门店数量'] > 0 or result_row['开店数'] > 0 or result_row['闭店数'] > 0:
                                        all_results.append(result_row)
    
    return pd.DataFrame(all_results)

def main(df, df2, current_month="2025_05"):
    """
    主函数
    df: 上月数据
    df2: 当月数据
    current_month: 当前月份，格式如 "2025_05"
    """
    try:
        logger.info("开始生成统计数据...")
        result_df = generate_comprehensive_statistics(df, df2, current_month)
        
        # 确保列顺序正确
        columns_order = ['province', 'city', 'FMCG_Type', 'FMCG_Chaintype', 
                        'FMCG_Channel', 'FMCG_Sub_Channel', 'is_chainstore', 
                        '面积', '品类', 'poi', '门店数量', '开店数', '闭店数']
        
        result_df = result_df[columns_order]
        
        logger.info(f"统计完成，共生成 {len(result_df)} 条记录")
        return result_df
        
    except Exception as exc:
        logger.error(f"流程执行失败: {exc}")
        raise
