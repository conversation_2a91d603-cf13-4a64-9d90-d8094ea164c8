import pandas as pd
import numpy as np
from itertools import combinations, product
from multiprocessing import Pool
import logging
from functools import lru_cache

logger = logging.getLogger(__name__)

def flatten_category_data_vectorized(df):
    """使用向量化操作扩展品类数据"""

    category_columns = ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                        '个人护理', '家庭护理', '粮油速食', '孕婴用品',
                        '宠物用品', '水饮冲调', '土特产']

    base_columns = ['province', 'city', '面积', 'FMCG_Type', 'FMCG_Chaintype',
                    'is_chainstore', 'FMCG_Channel', 'FMCG_Sub_Channel',
                    'create_time', 'close_time']

    expanded_dfs = []

    # 处理综合品类
    mask_综合 = df['综合'] > 0
    if mask_综合.any():
        temp_df = df[mask_综合][base_columns].copy()
        temp_df['品类'] = '综合'
        expanded_dfs.append(temp_df)

    # 处理土特产品类
    mask_土特产 = df['土特产'] > 0
    if mask_土特产.any():
        temp_df = df[mask_土特产][base_columns].copy()
        temp_df['品类'] = '土特产'
        expanded_dfs.append(temp_df)

    # 处理其他品类
    for cat in category_columns:
        if cat not in ['综合', '土特产']:
            mask = (df[cat] > 0) | (df['综合'] > 0)
            if mask.any():
                temp_df = df[mask][base_columns].copy()
                temp_df['品类'] = cat
                expanded_dfs.append(temp_df)

    return pd.concat(expanded_dfs, ignore_index=True)

def flatten_poi_data_vectorized(df):
    """使用向量化操作扩展POI数据"""
    if df.empty:
        return pd.DataFrame()

    poi_columns = ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店',
                   '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']

    base_columns = ['province', 'city', '面积', 'FMCG_Type', 'FMCG_Chaintype',
                    'is_chainstore', 'FMCG_Channel', 'FMCG_Sub_Channel',
                    'create_time', 'close_time']

    expanded_dfs = []

    for poi in poi_columns:
        mask = df[poi] > 0
        if mask.any():
            temp_df = df[mask][base_columns].copy()
            temp_df['poi'] = poi
            expanded_dfs.append(temp_df)

    return pd.concat(expanded_dfs, ignore_index=True) if expanded_dfs else pd.DataFrame()

def flatten_category_and_poi_data_vectorized(df):
    """使用向量化操作进行品类和POI数据完整扩展"""
    if df.empty:
        return pd.DataFrame()

    category_columns = ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                        '个人护理', '家庭护理', '粮油速食', '孕婴用品',
                        '宠物用品', '水饮冲调', '土特产']
    poi_columns = ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店',
                   '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']

    base_columns = ['province', 'city', '面积', 'FMCG_Type', 'FMCG_Chaintype',
                    'is_chainstore', 'FMCG_Channel', 'FMCG_Sub_Channel',
                    'create_time', 'close_time']

    expanded_dfs = []

    # 处理综合品类
    for poi in poi_columns:
        mask = (df['综合'] > 0) & (df[poi] > 0)
        if mask.any():
            temp_df = df[mask][base_columns].copy()
            temp_df['品类'] = '综合'
            temp_df['poi'] = poi
            expanded_dfs.append(temp_df)

    # 处理土特产品类
    for poi in poi_columns:
        mask = (df['土特产'] > 0) & (df[poi] > 0)
        if mask.any():
            temp_df = df[mask][base_columns].copy()
            temp_df['品类'] = '土特产'
            temp_df['poi'] = poi
            expanded_dfs.append(temp_df)

    # 处理其他品类
    for cat in category_columns:
        if cat not in ['综合', '土特产']:
            for poi in poi_columns:
                mask = ((df[cat] > 0) | (df['综合'] > 0)) & (df[poi] > 0)
                if mask.any():
                    temp_df = df[mask][base_columns].copy()
                    temp_df['品类'] = cat
                    temp_df['poi'] = poi
                    expanded_dfs.append(temp_df)

    return pd.concat(expanded_dfs, ignore_index=True) if expanded_dfs else pd.DataFrame()

@lru_cache(maxsize=128)
def get_expanded_dataframe(df_id, expansion_type):
    """缓存扩展后的数据框"""
    # 这里需要通过某种方式获取原始DataFrame
    # 实际使用时需要配合全局缓存机制
    pass

def get_appropriate_dataframe_optimized(df, category, poi):
    """优化版本：根据需求获取合适的数据框"""
    if category is not None and poi is not None:
        # 需要品类和POI：使用完整扩展
        return flatten_category_and_poi_data_vectorized(df)
    elif category is not None and poi is None:
        # 只需要品类：只扩展品类
        return flatten_category_data_vectorized(df)
    elif category is None and poi is not None:
        # 只需要POI：只扩展POI
        return flatten_poi_data_vectorized(df)
    else:
        # 都不需要：使用原始数据
        return df

def apply_filters_vectorized(df, **filters):
    """使用向量化操作应用筛选条件"""

    mask = pd.Series(True, index=df.index)

    for key, value in filters.items():
        if value is not None and key in df.columns:
            mask &= (df[key] == value)

    return df[mask]

def generate_comprehensive_statistics_optimized(df, df2, current_month):
    """优化版本：生成全面的统计数据"""
    all_results = []

    # 预先计算所有可能的值
    provinces = [None] + list(df['province'].dropna().unique())
    areas = [None, '小90', '90-300', '300-500', '500-1000', '1000-2000', '>2000']
    categories = [None] + ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                          '个人护理', '家庭护理', '粮油速食', '孕婴用品', '宠物用品', '水饮冲调', '土特产']
    pois = [None] + ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店',
                    '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']
    chainstores = [None, 0, 1]

    # 预先扩展数据（只扩展一次）
    logger.info("预先扩展品类数据...")
    df_category = flatten_category_data_vectorized(df)
    df2_category = flatten_category_data_vectorized(df2)

    logger.info("预先扩展POI数据...")
    df_poi = flatten_poi_data_vectorized(df)
    df2_poi = flatten_poi_data_vectorized(df2)

    logger.info("预先扩展品类+POI数据...")
    df_category_poi = flatten_category_and_poi_data_vectorized(df)
    df2_category_poi = flatten_category_and_poi_data_vectorized(df2)

    # 预先计算开店和闭店数据
    df_create = df2[df2['create_time'] == current_month] 
    df_close = df[df['close_time'] == current_month]

    logger.info("开始生成统计数据...")

    # 使用更高效的循环结构
    for province in provinces:
        # 根据省份筛选城市
        if province is None:
            cities = [None]
        else:
            cities = [None] + list(df[df['province'] == province]['city'].dropna().unique())

        for city in cities:
            for area in areas:
                for is_chainstore in chainstores:
                    # 基础筛选条件
                    base_filters = {
                        'province': province,
                        'city': city,
                        '面积': area,
                        'is_chainstore': is_chainstore
                    }

                    # 应用基础筛选
                    base_df = apply_filters_vectorized(df, **base_filters)
                    base_df2 = apply_filters_vectorized(df2, **base_filters)
                    base_df_create = apply_filters_vectorized(df_create, **base_filters)
                    base_df_close = apply_filters_vectorized(df_close, **base_filters)

                    if base_df.empty and base_df2.empty:
                        continue

                    # 处理不同的品类和POI组合
                    for category in categories:
                        for poi in pois:
                            # 选择合适的数据源
                            if category is not None and poi is not None:
                                current_df = apply_filters_vectorized(df_category_poi, **base_filters)
                                current_df2 = apply_filters_vectorized(df2_category_poi, **base_filters)
                                current_df_create = apply_filters_vectorized(
                                    flatten_category_and_poi_data_vectorized(base_df_create), 品类=category, poi=poi
                                ) if not base_df_create.empty else pd.DataFrame()
                                current_df_close = apply_filters_vectorized(
                                    flatten_category_and_poi_data_vectorized(base_df_close), 品类=category, poi=poi
                                ) if not base_df_close.empty else pd.DataFrame()
                            elif category is not None:
                                current_df = apply_filters_vectorized(df_category, **base_filters)
                                current_df2 = apply_filters_vectorized(df2_category, **base_filters)
                                current_df_create = apply_filters_vectorized(
                                    flatten_category_data_vectorized(base_df_create), 品类=category
                                ) if not base_df_create.empty else pd.DataFrame()
                                current_df_close = apply_filters_vectorized(
                                    flatten_category_data_vectorized(base_df_close), 品类=category
                                ) if not base_df_close.empty else pd.DataFrame()
                            elif poi is not None:
                                current_df = apply_filters_vectorized(df_poi, **base_filters)
                                current_df2 = apply_filters_vectorized(df2_poi, **base_filters)
                                current_df_create = apply_filters_vectorized(
                                    flatten_poi_data_vectorized(base_df_create), poi=poi
                                ) if not base_df_create.empty else pd.DataFrame()
                                current_df_close = apply_filters_vectorized(
                                    flatten_poi_data_vectorized(base_df_close), poi=poi
                                ) if not base_df_close.empty else pd.DataFrame()
                            else:
                                current_df = base_df
                                current_df2 = base_df2
                                current_df_create = base_df_create
                                current_df_close = base_df_close

                            # 应用品类和POI筛选
                            if category is not None and '品类' in current_df.columns:
                                current_df = current_df[current_df['品类'] == category]
                                current_df2 = current_df2[current_df2['品类'] == category]
                            if poi is not None and 'poi' in current_df.columns:
                                current_df = current_df[current_df['poi'] == poi]
                                current_df2 = current_df2[current_df2['poi'] == poi]

                            if current_df.empty and current_df2.empty:
                                continue

                            # 处理FMCG_Type和FMCG_Chaintype组合
                            _process_fmcg_combinations(
                                current_df, current_df2, current_df_create, current_df_close,
                                base_filters, category, poi, all_results, 'type'
                            )

                            # 处理FMCG_Channel和FMCG_Sub_Channel组合
                            _process_fmcg_combinations(
                                current_df, current_df2, current_df_create, current_df_close,
                                base_filters, category, poi, all_results, 'channel'
                            )

    return pd.DataFrame(all_results)

def _process_fmcg_combinations(current_df, current_df2, current_df_create, current_df_close,
                              base_filters, category, poi, all_results, combination_type):
    """处理FMCG组合的辅助方法"""
    if combination_type == 'type':
        primary_col = 'FMCG_Type'
        secondary_col = 'FMCG_Chaintype'
        other_primary = None
        other_secondary = None
    else:  # channel
        primary_col = 'FMCG_Channel'
        secondary_col = 'FMCG_Sub_Channel'
        other_primary = None
        other_secondary = None

    # 获取所有可能的值
    primary_values = [None] + list(current_df[primary_col].dropna().unique()) if len(current_df) > 0 else [None]

    for primary_val in primary_values:
        if primary_val is None:
            secondary_values = [None]
        else:
            secondary_values = [None] + list(
                current_df[current_df[primary_col] == primary_val][secondary_col].dropna().unique()
            )

        for secondary_val in secondary_values:
            # 应用筛选
            filters = {primary_col: primary_val, secondary_col: secondary_val}
            filter_df = apply_filters_vectorized(current_df, **filters)
            filter_df2 = apply_filters_vectorized(current_df2, **filters)
            filter_df_create = apply_filters_vectorized(current_df_create, **filters)
            filter_df_close = apply_filters_vectorized(current_df_close, **filters)

            # 构建结果行
            result_row = {
                'province': base_filters['province'],
                'city': base_filters['city'],
                'is_chainstore': base_filters['is_chainstore'],
                '面积': base_filters['面积'],
                '品类': category,
                'poi': poi,
                '门店数量': len(filter_df),
                '开店数': len(filter_df_create),
                '闭店数': len(filter_df_close)
            }

            if combination_type == 'type':
                result_row.update({
                    'FMCG_Type': primary_val,
                    'FMCG_Chaintype': secondary_val,
                    'FMCG_Channel': None,
                    'FMCG_Sub_Channel': None
                })
            else:
                result_row.update({
                    'FMCG_Type': None,
                    'FMCG_Chaintype': None,
                    'FMCG_Channel': primary_val,
                    'FMCG_Sub_Channel': secondary_val
                })

            if result_row['门店数量'] > 0 or result_row['开店数'] > 0 or result_row['闭店数'] > 0:
                all_results.append(result_row)

def generate_comprehensive_statistics(df, df2, current_month):
    """生成全面的统计数据 - 使用优化版本"""
    return generate_comprehensive_statistics_optimized(df, df2, current_month)

def main(df, df2, current_month="2025_05", use_optimized=True):
    """
    主函数
    df: 上月数据
    df2: 当月数据
    current_month: 当前月份，格式如 "2025_05"
    use_optimized: 是否使用优化版本，默认True
    """
    try:
        logger.info("开始生成统计数据...")

        if use_optimized:
            logger.info("使用优化版本进行处理...")
            result_df = generate_comprehensive_statistics_optimized(df, df2, current_month)
        else:
            logger.info("使用原始版本进行处理...")
            # 这里可以保留原始版本作为备用
            result_df = generate_comprehensive_statistics(df, df2, current_month)

        # 确保列顺序正确
        columns_order = ['province', 'city', 'FMCG_Type', 'FMCG_Chaintype',
                        'FMCG_Channel', 'FMCG_Sub_Channel', 'is_chainstore',
                        '面积', '品类', 'poi', '门店数量', '开店数', '闭店数']

        result_df = result_df[columns_order]

        logger.info(f"统计完成，共生成 {len(result_df)} 条记录")
        return result_df

    except Exception as exc:
        logger.error(f"流程执行失败: {exc}")
        raise

def benchmark_performance(df, df2, current_month="2025_05"):
    """性能基准测试函数"""
    import time

    # 测试优化版本
    start_time = time.time()
    result_optimized = main(df, df2, current_month, use_optimized=True)
    optimized_time = time.time() - start_time

    logger.info(f"执行时间: {optimized_time:.2f}秒")
    logger.info(f"结果数量: {len(result_optimized)}")

    return result_optimized, optimized_time
