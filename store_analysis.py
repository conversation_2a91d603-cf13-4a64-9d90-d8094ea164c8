import pandas as pd
import numpy as np
from itertools import combinations, product
from multiprocessing import Pool, Manager
import logging
from functools import lru_cache, partial
import time

logger = logging.getLogger(__name__)

def flatten_category_data_vectorized(df):
    """使用向量化操作扩展品类数据"""

    category_columns = ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                        '个人护理', '家庭护理', '粮油速食', '孕婴用品',
                        '宠物用品', '水饮冲调', '土特产']

    base_columns = ['province', 'city', '面积', 'FMCG_Type', 'FMCG_Chaintype',
                    'is_chainstore', 'FMCG_Channel', 'FMCG_Sub_Channel',
                    'create_time', 'close_time']

    expanded_dfs = []

    # 处理综合品类
    mask_综合 = df['综合'] > 0
    if mask_综合.any():
        temp_df = df[mask_综合][base_columns].copy()
        temp_df['品类'] = '综合'
        expanded_dfs.append(temp_df)

    # 处理土特产品类
    mask_土特产 = df['土特产'] > 0
    if mask_土特产.any():
        temp_df = df[mask_土特产][base_columns].copy()
        temp_df['品类'] = '土特产'
        expanded_dfs.append(temp_df)

    # 处理其他品类
    for cat in category_columns:
        if cat not in ['综合', '土特产']:
            mask = (df[cat] > 0) | (df['综合'] > 0)
            if mask.any():
                temp_df = df[mask][base_columns].copy()
                temp_df['品类'] = cat
                expanded_dfs.append(temp_df)

    return pd.concat(expanded_dfs, ignore_index=True)

def flatten_poi_data_vectorized(df):
    """使用向量化操作扩展POI数据"""
    if df.empty:
        return pd.DataFrame()

    poi_columns = ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店',
                   '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']

    base_columns = ['province', 'city', '面积', 'FMCG_Type', 'FMCG_Chaintype',
                    'is_chainstore', 'FMCG_Channel', 'FMCG_Sub_Channel',
                    'create_time', 'close_time']

    expanded_dfs = []

    for poi in poi_columns:
        mask = df[poi] > 0
        if mask.any():
            temp_df = df[mask][base_columns].copy()
            temp_df['poi'] = poi
            expanded_dfs.append(temp_df)

    return pd.concat(expanded_dfs, ignore_index=True) if expanded_dfs else pd.DataFrame()

def flatten_category_and_poi_data_vectorized(df):
    """使用向量化操作进行品类和POI数据完整扩展"""
    if df.empty:
        return pd.DataFrame()

    category_columns = ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                        '个人护理', '家庭护理', '粮油速食', '孕婴用品',
                        '宠物用品', '水饮冲调', '土特产']
    poi_columns = ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店',
                   '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']

    base_columns = ['province', 'city', '面积', 'FMCG_Type', 'FMCG_Chaintype',
                    'is_chainstore', 'FMCG_Channel', 'FMCG_Sub_Channel',
                    'create_time', 'close_time']

    expanded_dfs = []

    # 处理综合品类
    for poi in poi_columns:
        mask = (df['综合'] > 0) & (df[poi] > 0)
        if mask.any():
            temp_df = df[mask][base_columns].copy()
            temp_df['品类'] = '综合'
            temp_df['poi'] = poi
            expanded_dfs.append(temp_df)

    # 处理土特产品类
    for poi in poi_columns:
        mask = (df['土特产'] > 0) & (df[poi] > 0)
        if mask.any():
            temp_df = df[mask][base_columns].copy()
            temp_df['品类'] = '土特产'
            temp_df['poi'] = poi
            expanded_dfs.append(temp_df)

    # 处理其他品类
    for cat in category_columns:
        if cat not in ['综合', '土特产']:
            for poi in poi_columns:
                mask = ((df[cat] > 0) | (df['综合'] > 0)) & (df[poi] > 0)
                if mask.any():
                    temp_df = df[mask][base_columns].copy()
                    temp_df['品类'] = cat
                    temp_df['poi'] = poi
                    expanded_dfs.append(temp_df)

    return pd.concat(expanded_dfs, ignore_index=True) if expanded_dfs else pd.DataFrame()

@lru_cache(maxsize=128)
def get_expanded_dataframe(df_id, expansion_type):
    """缓存扩展后的数据框"""
    # 这里需要通过某种方式获取原始DataFrame
    # 实际使用时需要配合全局缓存机制
    pass

def get_appropriate_dataframe_optimized(df, category, poi):
    """优化版本：根据需求获取合适的数据框"""
    if category is not None and poi is not None:
        # 需要品类和POI：使用完整扩展
        return flatten_category_and_poi_data_vectorized(df)
    elif category is not None and poi is None:
        # 只需要品类：只扩展品类
        return flatten_category_data_vectorized(df)
    elif category is None and poi is not None:
        # 只需要POI：只扩展POI
        return flatten_poi_data_vectorized(df)
    else:
        # 都不需要：使用原始数据
        return df

def apply_filters_vectorized(df, **filters):
    """使用向量化操作应用筛选条件"""
    if df.empty:
        return df

    mask = pd.Series(True, index=df.index)

    for key, value in filters.items():
        if value is not None and key in df.columns:
            mask &= (df[key] == value)

    return df[mask]

def process_province_chunk(args):
    """处理单个省份的数据 - 多进程工作函数"""
    province, df_dict, df2_dict, current_month = args

    # 重建DataFrame（多进程需要）
    df = pd.DataFrame(df_dict)
    df2 = pd.DataFrame(df2_dict)

    all_results = []
    areas = [None, '小90', '90-300', '300-500', '500-1000', '1000-2000', '>2000']
    categories = [None] + ['综合', '生鲜果蔬', '休闲食品', '低温冰品', '中外名酒',
                          '个人护理', '家庭护理', '粮油速食', '孕婴用品', '宠物用品', '水饮冲调', '土特产']
    pois = [None] + ['便利店', '超市', '地铁站', '交通枢纽', '景点', '酒店',
                    '商超百货', '写字楼', '休闲娱乐', '学校', '医院', '住宅区']
    chainstores = [None, 0, 1]

    # 预先扩展当前省份的数据
    df_category = flatten_category_data_vectorized(df)
    df2_category = flatten_category_data_vectorized(df2)
    df_poi = flatten_poi_data_vectorized(df)
    df2_poi = flatten_poi_data_vectorized(df2)
    df_category_poi = flatten_category_and_poi_data_vectorized(df)
    df2_category_poi = flatten_category_and_poi_data_vectorized(df2)

    # 预先计算开店和闭店数据
    df_create = df2[df2['create_time'] == current_month] if 'create_time' in df2.columns else pd.DataFrame()
    df_close = df[df['close_time'] == current_month] if 'close_time' in df.columns else pd.DataFrame()

    # 根据省份筛选城市
    if province is None:
        cities = [None]
    else:
        cities = [None] + list(df[df['province'] == province]['city'].dropna().unique())

    for city in cities:
        for area in areas:
            for is_chainstore in chainstores:
                # 基础筛选条件
                base_filters = {
                    'province': province,
                    'city': city,
                    '面积': area,
                    'is_chainstore': is_chainstore
                }

                # 应用基础筛选
                base_df = apply_filters_vectorized(df, **base_filters)
                base_df2 = apply_filters_vectorized(df2, **base_filters)

                if base_df.empty and base_df2.empty:
                    continue

                # 处理不同的品类和POI组合
                for category in categories:
                    for poi in pois:
                        # 选择合适的数据源
                        if category is not None and poi is not None:
                            current_df = apply_filters_vectorized(df_category_poi, **base_filters)
                            current_df2 = apply_filters_vectorized(df2_category_poi, **base_filters)
                        elif category is not None:
                            current_df = apply_filters_vectorized(df_category, **base_filters)
                            current_df2 = apply_filters_vectorized(df2_category, **base_filters)
                        elif poi is not None:
                            current_df = apply_filters_vectorized(df_poi, **base_filters)
                            current_df2 = apply_filters_vectorized(df2_poi, **base_filters)
                        else:
                            current_df = base_df
                            current_df2 = base_df2

                        # 应用品类和POI筛选
                        if category is not None and '品类' in current_df.columns:
                            current_df = current_df[current_df['品类'] == category]
                            current_df2 = current_df2[current_df2['品类'] == category]
                        if poi is not None and 'poi' in current_df.columns:
                            current_df = current_df[current_df['poi'] == poi]
                            current_df2 = current_df2[current_df2['poi'] == poi]

                        if current_df.empty and current_df2.empty:
                            continue

                        # 计算开店闭店数据
                        current_df_create = apply_filters_vectorized(df_create, **base_filters)
                        current_df_close = apply_filters_vectorized(df_close, **base_filters)

                        if category is not None:
                            if not current_df_create.empty:
                                temp_create = flatten_category_data_vectorized(current_df_create)
                                current_df_create = temp_create[temp_create['品类'] == category] if '品类' in temp_create.columns else pd.DataFrame()
                            if not current_df_close.empty:
                                temp_close = flatten_category_data_vectorized(current_df_close)
                                current_df_close = temp_close[temp_close['品类'] == category] if '品类' in temp_close.columns else pd.DataFrame()

                        if poi is not None:
                            if not current_df_create.empty:
                                temp_create = flatten_poi_data_vectorized(current_df_create)
                                current_df_create = temp_create[temp_create['poi'] == poi] if 'poi' in temp_create.columns else pd.DataFrame()
                            if not current_df_close.empty:
                                temp_close = flatten_poi_data_vectorized(current_df_close)
                                current_df_close = temp_close[temp_close['poi'] == poi] if 'poi' in temp_close.columns else pd.DataFrame()

                        # 处理FMCG_Type和FMCG_Chaintype组合
                        _process_fmcg_combinations(
                            current_df, current_df2, current_df_create, current_df_close,
                            base_filters, category, poi, all_results, 'type'
                        )

                        # 处理FMCG_Channel和FMCG_Sub_Channel组合
                        _process_fmcg_combinations(
                            current_df, current_df2, current_df_create, current_df_close,
                            base_filters, category, poi, all_results, 'channel'
                        )

    return all_results

def generate_comprehensive_statistics_multiprocess(df, df2, current_month, num_cores=None):
    """多进程版本：生成全面的统计数据"""
    if num_cores is None:
        import os
        num_cores = min(os.cpu_count(), 22)  # 限制最大核心数

    logger.info(f"使用 {num_cores} 个进程进行并行处理...")

    # 获取所有省份
    provinces = [None] + list(df['province'].dropna().unique())

    # 准备多进程参数
    args_list = []
    for province in provinces:
        # 筛选当前省份的数据
        if province is None:
            province_df = df.copy()
            province_df2 = df2.copy()
        else:
            province_df = df[df['province'] == province].copy()
            province_df2 = df2[df2['province'] == province].copy()

        if not province_df.empty or not province_df2.empty:
            # 转换为字典以便多进程传递
            df_dict = province_df.to_dict('records')
            df2_dict = province_df2.to_dict('records')
            args_list.append((province, df_dict, df2_dict, current_month))

    logger.info(f"准备处理 {len(args_list)} 个省份/区域...")

    # 多进程处理
    start_time = time.time()
    with Pool(num_cores) as pool:
        results = pool.map(process_province_chunk, args_list)

    process_time = time.time() - start_time
    logger.info(f"多进程处理完成，耗时: {process_time:.2f}秒")

    # 合并结果
    all_results = []
    for result in results:
        all_results.extend(result)

    logger.info(f"合并结果完成，共生成 {len(all_results)} 条记录")
    return pd.DataFrame(all_results)

def _process_fmcg_combinations(current_df, current_df2, current_df_create, current_df_close,
                              base_filters, category, poi, all_results, combination_type):
    """处理FMCG组合的辅助方法"""
    if combination_type == 'type':
        primary_col = 'FMCG_Type'
        secondary_col = 'FMCG_Chaintype'
        other_primary = None
        other_secondary = None
    else:  # channel
        primary_col = 'FMCG_Channel'
        secondary_col = 'FMCG_Sub_Channel'
        other_primary = None
        other_secondary = None

    # 获取所有可能的值
    primary_values = [None] + list(current_df[primary_col].dropna().unique()) if len(current_df) > 0 else [None]

    for primary_val in primary_values:
        if primary_val is None:
            secondary_values = [None]
        else:
            secondary_values = [None] + list(
                current_df[current_df[primary_col] == primary_val][secondary_col].dropna().unique()
            )

        for secondary_val in secondary_values:
            # 应用筛选
            filters = {primary_col: primary_val, secondary_col: secondary_val}
            filter_df = apply_filters_vectorized(current_df, **filters)
            filter_df2 = apply_filters_vectorized(current_df2, **filters)
            filter_df_create = apply_filters_vectorized(current_df_create, **filters)
            filter_df_close = apply_filters_vectorized(current_df_close, **filters)

            # 构建结果行
            result_row = {
                'province': base_filters['province'],
                'city': base_filters['city'],
                'is_chainstore': base_filters['is_chainstore'],
                '面积': base_filters['面积'],
                '品类': category,
                'poi': poi,
                '门店数量': len(filter_df),
                '开店数': len(filter_df_create),
                '闭店数': len(filter_df_close)
            }

            if combination_type == 'type':
                result_row.update({
                    'FMCG_Type': primary_val,
                    'FMCG_Chaintype': secondary_val,
                    'FMCG_Channel': None,
                    'FMCG_Sub_Channel': None
                })
            else:
                result_row.update({
                    'FMCG_Type': None,
                    'FMCG_Chaintype': None,
                    'FMCG_Channel': primary_val,
                    'FMCG_Sub_Channel': secondary_val
                })

            if result_row['门店数量'] > 0 or result_row['开店数'] > 0 or result_row['闭店数'] > 0:
                all_results.append(result_row)

def generate_comprehensive_statistics(df, df2, current_month):
    """生成全面的统计数据 - 使用多进程优化版本"""
    return generate_comprehensive_statistics_multiprocess(df, df2, current_month)

def main(df, df2, current_month="2025_05", method='auto', num_cores=None, chunk_size=100000):
    """
    主函数
    df: 上月数据
    df2: 当月数据
    current_month: 当前月份，格式如 "2025_05"
    method: 处理方法 ('auto', 'multiprocess', 'chunked', 'single')
    num_cores: 使用的CPU核心数，默认自动检测
    chunk_size: 分块大小（仅在chunked模式下使用）
    """
    try:
        logger.info("开始生成统计数据...")
        logger.info(f"数据规模: df={len(df):,}行, df2={len(df2):,}行")

        # 自动选择最佳处理方法
        if method == 'auto':
            if len(df) > 1000000:  # 超过100万行使用分块处理
                method = 'chunked'
                logger.info("数据量较大，自动选择分块多进程处理")
            elif len(df) > 100000:  # 超过10万行使用多进程
                method = 'multiprocess'
                logger.info("自动选择多进程处理")
            else:
                method = 'single'
                logger.info("数据量较小，使用单进程处理")

        start_time = time.time()

        if method == 'chunked':
            logger.info(f"使用分块多进程处理 (块大小: {chunk_size:,}行)...")
            result_df = generate_comprehensive_statistics_chunked(df, df2, current_month, chunk_size, num_cores)
        elif method == 'multiprocess':
            logger.info("使用多进程处理...")
            result_df = generate_comprehensive_statistics_multiprocess(df, df2, current_month, num_cores)
        else:  # single
            logger.info("使用单进程处理...")
            result_df = generate_comprehensive_statistics(df, df2, current_month)

        total_time = time.time() - start_time

        # 确保列顺序正确
        columns_order = ['province', 'city', 'FMCG_Type', 'FMCG_Chaintype',
                        'FMCG_Channel', 'FMCG_Sub_Channel', 'is_chainstore',
                        '面积', '品类', 'poi', '门店数量', '开店数', '闭店数']

        result_df = result_df[columns_order]

        logger.info(f"统计完成！")
        logger.info(f"处理方法: {method}")
        logger.info(f"总执行时间: {total_time:.2f}秒")
        logger.info(f"生成记录数: {len(result_df):,}条")
        logger.info(f"平均处理速度: {len(df)/total_time:.0f}行/秒")

        return result_df

    except Exception as exc:
        logger.error(f"流程执行失败: {exc}")
        raise

def benchmark_performance(df, df2, current_month="2025_05", test_cores=[1, 4, 8, 16]):
    """性能基准测试函数 - 测试不同核心数的性能"""
    results = {}

    logger.info("开始性能基准测试...")
    logger.info(f"数据规模: df={len(df):,}行, df2={len(df2):,}行")

    for cores in test_cores:
        if cores > 1:
            logger.info(f"\n测试多进程版本 ({cores}核心)...")
            start_time = time.time()
            result = main(df, df2, current_month, use_multiprocess=True, num_cores=cores)
            exec_time = time.time() - start_time

            results[f'{cores}核心'] = {
                'time': exec_time,
                'records': len(result),
                'speed': len(df) / exec_time
            }

            logger.info(f"{cores}核心执行时间: {exec_time:.2f}秒")
            logger.info(f"处理速度: {len(df)/exec_time:.0f}行/秒")
        else:
            logger.info(f"\n测试单进程版本...")
            start_time = time.time()
            result = main(df, df2, current_month, use_multiprocess=False)
            exec_time = time.time() - start_time

            results['单进程'] = {
                'time': exec_time,
                'records': len(result),
                'speed': len(df) / exec_time
            }

            logger.info(f"单进程执行时间: {exec_time:.2f}秒")
            logger.info(f"处理速度: {len(df)/exec_time:.0f}行/秒")

    # 输出性能对比
    logger.info("\n=== 性能对比结果 ===")
    baseline_time = results.get('单进程', {}).get('time', 0)

    for name, stats in results.items():
        speedup = baseline_time / stats['time'] if baseline_time > 0 else 1
        logger.info(f"{name}: {stats['time']:.2f}秒, {stats['speed']:.0f}行/秒, 加速比: {speedup:.1f}x")

    return results

def generate_comprehensive_statistics_chunked(df, df2, current_month, chunk_size=100000, num_cores=None):
    """分块多进程版本：适用于超大数据集"""
    if num_cores is None:
        import os
        num_cores = min(os.cpu_count(), 22)

    logger.info(f"使用分块多进程处理，块大小: {chunk_size:,}行, 进程数: {num_cores}")

    # 按行分块
    chunks = []
    for i in range(0, len(df), chunk_size):
        chunk_df = df.iloc[i:i+chunk_size].copy()
        chunk_df2 = df2.iloc[i:i+chunk_size].copy() if i < len(df2) else pd.DataFrame()

        if not chunk_df.empty:
            chunks.append((chunk_df.to_dict('records'), chunk_df2.to_dict('records'), current_month, i))

    logger.info(f"数据分为 {len(chunks)} 个块进行处理")

    # 多进程处理分块
    def process_chunk(args):
        chunk_df_dict, chunk_df2_dict, current_month, chunk_id = args
        chunk_df = pd.DataFrame(chunk_df_dict)
        chunk_df2 = pd.DataFrame(chunk_df2_dict)

        logger.info(f"处理块 {chunk_id//chunk_size + 1}/{len(chunks)}")

        # 对每个块使用省份级别的多进程处理
        return generate_comprehensive_statistics_multiprocess(chunk_df, chunk_df2, current_month, num_cores=2)

    start_time = time.time()
    with Pool(min(num_cores, len(chunks))) as pool:
        chunk_results = pool.map(process_chunk, chunks)

    process_time = time.time() - start_time
    logger.info(f"分块处理完成，耗时: {process_time:.2f}秒")

    # 合并所有块的结果
    all_results = []
    for chunk_result in chunk_results:
        if not chunk_result.empty:
            all_results.append(chunk_result)

    if all_results:
        final_result = pd.concat(all_results, ignore_index=True)
        # 去重并聚合
        final_result = final_result.groupby([
            'province', 'city', 'FMCG_Type', 'FMCG_Chaintype',
            'FMCG_Channel', 'FMCG_Sub_Channel', 'is_chainstore',
            '面积', '品类', 'poi'
        ]).agg({
            '门店数量': 'sum',
            '开店数': 'sum',
            '闭店数': 'sum'
        }).reset_index()

        logger.info(f"分块处理完成，最终生成 {len(final_result):,} 条记录")
        return final_result
    else:
        return pd.DataFrame()
